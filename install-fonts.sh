#!/bin/bash

# Font Installation Script for AWS Server
# This script installs required fonts and libraries for chartjs-node-canvas

echo "🔧 Installing fonts and libraries for chart rendering..."

# Detect OS
if [ -f /etc/os-release ]; then
    . /etc/os-release
    OS=$NAME
    VER=$VERSION_ID
elif type lsb_release >/dev/null 2>&1; then
    OS=$(lsb_release -si)
    VER=$(lsb_release -sr)
elif [ -f /etc/lsb-release ]; then
    . /etc/lsb-release
    OS=$DISTRIB_ID
    VER=$DISTRIB_RELEASE
elif [ -f /etc/debian_version ]; then
    OS=Debian
    VER=$(cat /etc/debian_version)
elif [ -f /etc/SuSe-release ]; then
    OS=openSUSE
elif [ -f /etc/redhat-release ]; then
    OS=RedHat
else
    OS=$(uname -s)
    VER=$(uname -r)
fi

echo "📊 Detected OS: $OS $VER"

# Install based on OS
if [[ "$OS" == *"Ubuntu"* ]] || [[ "$OS" == *"Debian"* ]]; then
    echo "🔧 Installing for Ubuntu/Debian..."
    
    # Update package list
    sudo apt update
    
    # Install build tools and libraries
    sudo apt install -y build-essential libcairo2-dev libpango1.0-dev libjpeg-dev libgif-dev librsvg2-dev
    
    # Install fonts
    sudo apt install -y fonts-liberation fonts-dejavu-core fontconfig
    sudo apt install -y fonts-noto fonts-noto-cjk fonts-noto-emoji
    sudo apt install -y fonts-roboto fonts-open-sans
    
    # Refresh font cache
    sudo fc-cache -fv
    
elif [[ "$OS" == *"Amazon Linux"* ]] || [[ "$OS" == *"CentOS"* ]] || [[ "$OS" == *"Red Hat"* ]]; then
    echo "🔧 Installing for Amazon Linux/CentOS/RHEL..."
    
    # Check if dnf is available (newer versions)
    if command -v dnf &> /dev/null; then
        PKG_MANAGER="dnf"
    else
        PKG_MANAGER="yum"
    fi
    
    echo "Using package manager: $PKG_MANAGER"
    
    # Update packages
    sudo $PKG_MANAGER update -y
    
    # Install development tools
    sudo $PKG_MANAGER groupinstall -y "Development Tools"
    
    # Install libraries
    sudo $PKG_MANAGER install -y cairo-devel pango-devel libjpeg-turbo-devel giflib-devel librsvg2-devel
    
    # Install fonts
    sudo $PKG_MANAGER install -y liberation-fonts dejavu-sans-fonts fontconfig
    
    # Try to install additional fonts (may not be available on all versions)
    sudo $PKG_MANAGER install -y google-noto-fonts google-noto-emoji-fonts 2>/dev/null || echo "⚠️ Some additional fonts not available"
    
    # Refresh font cache
    sudo fc-cache -fv
    
else
    echo "❌ Unsupported OS: $OS"
    echo "Please install the following manually:"
    echo "- Build tools (gcc, make, etc.)"
    echo "- Cairo development libraries"
    echo "- Pango development libraries"
    echo "- JPEG development libraries"
    echo "- GIF development libraries"
    echo "- SVG development libraries"
    echo "- Liberation fonts or similar"
    exit 1
fi

echo ""
echo "✅ Font installation completed!"
echo ""
echo "📋 Installed components:"
echo "  - Build tools and development libraries"
echo "  - Cairo and Pango libraries for canvas rendering"
echo "  - Liberation fonts for text rendering"
echo "  - Additional fonts for better compatibility"
echo ""
echo "🔄 Next steps:"
echo "  1. Restart your Node.js application"
echo "  2. Test chart generation"
echo "  3. If issues persist, check the logs for specific errors"
echo ""

# Test font availability
echo "🧪 Testing font availability..."
fc-list | grep -i liberation | head -3
fc-list | grep -i dejavu | head -3

echo ""
echo "🎯 Installation script completed!"
echo "You can now restart your trading bot with PM2"

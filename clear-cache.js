#!/usr/bin/env node

/**
 * Cache clearing utility for AWS server
 * Run this script to clear all cached data
 */

import { clearAllCaches, getGlobalCacheStatus } from './src/BinanceAPI.js';

async function clearCache() {
  console.log('🧹 Cache Clearing Utility');
  console.log('========================');
  
  // Show current cache status
  console.log('\n📊 Current cache status:');
  const beforeStatus = getGlobalCacheStatus();
  
  if (Object.keys(beforeStatus).length === 0) {
    console.log('✅ No cached data found');
  } else {
    Object.entries(beforeStatus).forEach(([symbol, timeframes]) => {
      console.log(`📈 ${symbol}:`);
      Object.entries(timeframes).forEach(([timeframe, status]) => {
        const validStatus = status.isValid ? '✅ Valid' : '❌ Invalid';
        console.log(`  ${timeframe}: ${validStatus} (${status.dataPoints} points)`);
      });
    });
  }
  
  // Clear all caches
  console.log('\n🧹 Clearing all caches...');
  clearAllCaches();
  
  // Verify cache is cleared
  console.log('\n📊 Cache status after clearing:');
  const afterStatus = getGlobalCacheStatus();
  
  if (Object.keys(afterStatus).length === 0) {
    console.log('✅ All caches successfully cleared!');
  } else {
    console.log('⚠️ Some cache data may still exist:');
    console.log(afterStatus);
  }
  
  console.log('\n🎯 Cache clearing completed!');
  console.log('You can now restart your bot with PM2');
}

// Run the cache clearing
clearCache().catch(console.error);

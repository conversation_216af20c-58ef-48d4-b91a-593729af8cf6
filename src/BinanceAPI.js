import axios from "axios";
import dotenv from "dotenv";

dotenv.config();

/**
 * Global cache manager to prevent cross-symbol contamination
 */
class CacheManager {
  constructor() {
    this.symbolCaches = new Map();
  }

  getCache(symbol) {
    if (!this.symbolCaches.has(symbol)) {
      this.symbolCaches.set(symbol, {
        "15m": { data: null, lastUpdate: 0, ttl: 15 * 60 * 1000, symbol },
        "1h": { data: null, lastUpdate: 0, ttl: 60 * 60 * 1000, symbol },
        "4h": { data: null, lastUpdate: 0, ttl: 4 * 60 * 60 * 1000, symbol },
      });
    }
    return this.symbolCaches.get(symbol);
  }

  clearCache(symbol = null, timeframe = null) {
    if (symbol) {
      if (timeframe) {
        const cache = this.symbolCaches.get(symbol);
        if (cache && cache[timeframe]) {
          cache[timeframe].data = null;
          cache[timeframe].lastUpdate = 0;
        }
      } else {
        this.symbolCaches.delete(symbol);
      }
    } else {
      this.symbolCaches.clear();
    }
  }

  getCacheStatus() {
    const status = {};
    for (const [symbol, cache] of this.symbolCaches.entries()) {
      const now = Date.now();
      status[symbol] = {};
      Object.entries(cache).forEach(([timeframe, cacheData]) => {
        const age = now - cacheData.lastUpdate;
        const isValid = cacheData.data && age < cacheData.ttl;
        status[symbol][timeframe] = {
          hasData: !!cacheData.data,
          isValid: isValid,
          ageMs: age,
          ttlMs: cacheData.ttl,
          dataPoints: cacheData.data ? cacheData.data.length : 0,
        };
      });
    }
    return status;
  }
}

const globalCacheManager = new CacheManager();

/**
 * BinanceAPI Class - Handles all Binance API interactions
 * Extracted from index.js to improve code organization and maintainability
 */
export class BinanceAPI {
  constructor(options = {}) {
    // API Configuration
    this.apiUrl = options.apiUrl || "https://api.binance.com/api/v3/klines";
    this.symbol = options.symbol || process.env.SYMBOL || "ETHUSDT";
    this.defaultInterval =
      options.defaultInterval || process.env.INTERVAL || "1h";

    // Multi-Timeframe Configuration
    this.timeframes = options.timeframes || {
      "15m": { interval: "15m", limit: 96, name: "15-minute" }, // 24 hours of 15m candles
      "1h": { interval: "1h", limit: 168, name: "1-hour" }, // 7 days of 1h candles
      "4h": { interval: "4h", limit: 180, name: "4-hour" }, // 30 days of 4h candles
    };

    // Use global cache manager for symbol-specific caching
    this.cacheManager = globalCacheManager;
  }

  /**
   * Get symbol-specific cache
   * @returns {Object} Cache object for this symbol
   */
  get dataCache() {
    return this.cacheManager.getCache(this.symbol);
  }

  /**
   * Fetch data for a specific timeframe
   * @param {string} timeframe - The timeframe to fetch (15m, 1h, 4h)
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchTimeframeData(timeframe) {
    const config = this.timeframes[timeframe];
    if (!config) {
      throw new Error(`Invalid timeframe: ${timeframe}`);
    }

    try {
      const { data } = await axios.get(this.apiUrl, {
        params: {
          symbol: this.symbol,
          interval: config.interval,
          limit: config.limit,
        },
      });

      return data.map((d) => ({
        time: d[0],
        open: parseFloat(d[1]),
        high: parseFloat(d[2]),
        low: parseFloat(d[3]),
        close: parseFloat(d[4]),
        volume: parseFloat(d[5]),
        closeTime: d[6],
        timeframe: timeframe,
      }));
    } catch (error) {
      console.error(`Error fetching ${timeframe} data:`, error.message);
      throw error;
    }
  }

  /**
   * Get cached data for a timeframe, fetch fresh if cache is expired
   * @param {string} timeframe - The timeframe to get data for
   * @returns {Promise<Array>} Array of candle data
   */
  async getCachedData(timeframe) {
    const cache = this.dataCache[timeframe];
    const now = Date.now();

    // Check if cache is valid and for the correct symbol
    if (
      cache.data &&
      now - cache.lastUpdate < cache.ttl &&
      cache.symbol === this.symbol
    ) {
      console.log(`Using cached data for ${timeframe} (${this.symbol})`);
      return cache.data;
    }

    // Fetch fresh data
    console.log(`Fetching fresh data for ${timeframe} (${this.symbol})`);
    const data = await this.fetchTimeframeData(timeframe);

    // Update cache with symbol tracking
    cache.data = data;
    cache.lastUpdate = now;
    cache.symbol = this.symbol;

    return data;
  }

  /**
   * Fetch all timeframes data concurrently
   * @returns {Promise<Object>} Object containing data for all timeframes
   */
  async fetchAllTimeframes() {
    try {
      const [data15m, data1h, data4h] = await Promise.all([
        this.getCachedData("15m"),
        this.getCachedData("1h"),
        this.getCachedData("4h"),
      ]);

      return {
        "15m": data15m,
        "1h": data1h,
        "4h": data4h,
      };
    } catch (error) {
      console.error("Error fetching multi-timeframe data:", error);
      throw error;
    }
  }

  /**
   * Legacy fetchData function for backward compatibility
   * @param {string} interval - Optional interval override
   * @param {number} limit - Optional limit override
   * @returns {Promise<Array>} Array of candle data
   */
  async fetchData(interval = null, limit = 240) {
    const { data } = await axios.get(this.apiUrl, {
      params: {
        symbol: this.symbol,
        interval: interval || this.defaultInterval,
        limit: limit,
      },
    });

    return data.map((d) => ({
      time: d[0],
      open: parseFloat(d[1]),
      high: parseFloat(d[2]),
      low: parseFloat(d[3]),
      close: parseFloat(d[4]),
      volume: parseFloat(d[5]),
      closeTime: d[6],
    }));
  }

  /**
   * Clear cache for a specific timeframe or all timeframes for this symbol
   * @param {string} timeframe - Optional specific timeframe to clear
   */
  clearCache(timeframe = null) {
    this.cacheManager.clearCache(this.symbol, timeframe);
    if (timeframe) {
      console.log(`Cache cleared for ${this.symbol} ${timeframe}`);
    } else {
      console.log(`All cache cleared for ${this.symbol}`);
    }
  }

  /**
   * Get cache status for all timeframes for this symbol
   * @returns {Object} Cache status information
   */
  getCacheStatus() {
    const allStatus = this.cacheManager.getCacheStatus();
    return allStatus[this.symbol] || {};
  }

  /**
   * Get global cache status for all symbols
   * @returns {Object} Global cache status information
   */
  getGlobalCacheStatus() {
    return this.cacheManager.getCacheStatus();
  }

  /**
   * Get current symbol
   * @returns {string} Current trading symbol
   */
  getSymbol() {
    return this.symbol;
  }

  /**
   * Set new symbol
   * @param {string} symbol - New trading symbol
   */
  setSymbol(symbol) {
    if (this.symbol !== symbol) {
      this.symbol = symbol;
      this.clearCache(); // Clear cache when symbol changes
      console.log(`Symbol changed to ${symbol}, cache cleared`);
    }
  }

  /**
   * Get timeframes configuration
   * @returns {Object} Timeframes configuration
   */
  getTimeframes() {
    return { ...this.timeframes };
  }

  /**
   * Get default interval
   * @returns {string} Default interval
   */
  getDefaultInterval() {
    return this.defaultInterval;
  }

  /**
   * Set default interval
   * @param {string} interval - New default interval
   */
  setDefaultInterval(interval) {
    this.defaultInterval = interval;
  }
}

// Create default instance for backward compatibility
export const binanceAPI = new BinanceAPI();

// Export individual functions for backward compatibility
export const fetchTimeframeData = (timeframe) =>
  binanceAPI.fetchTimeframeData(timeframe);
export const getCachedData = (timeframe) => binanceAPI.getCachedData(timeframe);
export const fetchAllTimeframes = () => binanceAPI.fetchAllTimeframes();
export const fetchData = (interval, limit) =>
  binanceAPI.fetchData(interval, limit);

// Export constants for backward compatibility
export const TIMEFRAMES = binanceAPI.getTimeframes();
export const dataCache = binanceAPI.dataCache;

// Export cache manager for debugging and global cache management
export const cacheManager = globalCacheManager;

// Utility function to clear all caches globally
export const clearAllCaches = () => {
  globalCacheManager.clearCache();
  console.log("🧹 All symbol caches cleared globally");
};

// Utility function to get global cache status
export const getGlobalCacheStatus = () => {
  return globalCacheManager.getCacheStatus();
};

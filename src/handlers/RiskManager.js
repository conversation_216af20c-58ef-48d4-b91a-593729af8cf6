// RiskManager.js - Conservative swing trading risk management
// Uses 4h timeframe for SL/TP, enforces >= 2:1 RR, and provides position sizing helpers

export class RiskManager {
  constructor(options = {}) {
    this.minRR = options.minRR ?? 2.0;
    // Defaults suitable for demos; user can set env vars to override
    this.riskPerTrade = parseFloat(process.env.RISK_PER_TRADE || "0.01"); // 1%
    this.accountBalanceUSDT = parseFloat(process.env.ACCOUNT_BALANCE_USDT || "0");
  }

  // Compute ATR on array of candles [{high, low, close}]
  calculateATR(candles, period = 14) {
    if (!candles || candles.length < period + 1) return 0;
    const trs = [];
    for (let i = 1; i < candles.length; i++) {
      const c = candles[i];
      const p = candles[i - 1];
      const tr = Math.max(c.high - c.low, Math.abs(c.high - p.close), Math.abs(c.low - p.close));
      trs.push(tr);
    }
    const recent = trs.slice(-period);
    return recent.reduce((s, v) => s + v, 0) / recent.length;
  }

  // Basic swing support/resistance from recent 4h candles
  findSwingLevels(candles4h, lookback = 20) {
    const recent = candles4h.slice(-lookback);
    const support = Math.min(...recent.map((c) => c.low));
    const resistance = Math.max(...recent.map((c) => c.high));
    return { support, resistance };
  }

  // Core: compute 4h-based SL/TP ensuring RR >= minRR; returns computed levels and rr
  calculateSwingRiskLevels({ candles4h, entryPrice, direction }) {
    if (!candles4h || !candles4h.length || !entryPrice || !direction) {
      return { valid: false, reason: "INSUFFICIENT_DATA" };
    }

    const atr4h = this.calculateATR(candles4h, 14) || 0;
    const { support, resistance } = this.findSwingLevels(candles4h, 30);

    let stopLoss, takeProfit1, takeProfit2;

    if (direction === "BULLISH" || direction === "LONG" || direction === "BUY") {
      // Conservative SL: below swing support and at least 1.0*ATR buffer
      const slCandidate = Math.min(support, entryPrice - atr4h);
      stopLoss = slCandidate;
      const risk = Math.max(1e-9, entryPrice - stopLoss);
      // TP1 at least minRR * risk; TP2 near resistance if reasonable
      takeProfit1 = entryPrice + this.minRR * risk;
      takeProfit2 = Math.min(resistance, entryPrice + Math.max(this.minRR * risk, 2 * atr4h));
    } else if (direction === "BEARISH" || direction === "SHORT" || direction === "SELL") {
      const slCandidate = Math.max(resistance, entryPrice + atr4h);
      stopLoss = slCandidate;
      const risk = Math.max(1e-9, stopLoss - entryPrice);
      takeProfit1 = entryPrice - this.minRR * risk;
      takeProfit2 = Math.max(support, entryPrice - Math.max(this.minRR * risk, 2 * atr4h));
    } else {
      return { valid: false, reason: "UNKNOWN_DIRECTION" };
    }

    const rr = this.computeRR({ entryPrice, stopLoss, takeProfit: takeProfit1 });
    const valid = rr >= this.minRR;

    return {
      valid,
      reason: valid ? "OK" : `MIN_RR_NOT_MET_${rr.toFixed(2)}`,
      support4h: support,
      resistance4h: resistance,
      atr4h,
      stopLoss,
      takeProfit1,
      takeProfit2,
      riskReward: rr,
      minRR: this.minRR,
    };
  }

  // RR helper (absolute distances)
  computeRR({ entryPrice, stopLoss, takeProfit }) {
    const risk = Math.abs(entryPrice - stopLoss);
    const reward = Math.abs(takeProfit - entryPrice);
    if (risk <= 0) return 0;
    return reward / risk;
  }

  // Validate explicit set of levels against min RR
  validateMinRR({ entryPrice, stopLoss, takeProfit }) {
    const rr = this.computeRR({ entryPrice, stopLoss, takeProfit });
    return { rr, valid: rr >= this.minRR };
  }

  // Position sizing given account balance and risk per trade; returns quantity at entry
  positionSize({ entryPrice, stopLoss, balanceUSDT = undefined, riskPct = undefined }) {
    const balance = balanceUSDT ?? this.accountBalanceUSDT;
    const riskPercent = riskPct ?? this.riskPerTrade;
    if (!balance || balance <= 0 || !entryPrice || !stopLoss) {
      return { quantity: 0, dollarRisk: 0, notes: "Missing balance or prices" };
    }
    const riskDollar = balance * riskPercent;
    const riskPerUnit = Math.abs(entryPrice - stopLoss);
    if (riskPerUnit <= 0) return { quantity: 0, dollarRisk: 0, notes: "Invalid SL/entry" };
    const qty = riskDollar / riskPerUnit; // in base asset units (approx)
    return { quantity: qty, dollarRisk: riskDollar, riskPercent };
  }
}

export const riskManager = new RiskManager();


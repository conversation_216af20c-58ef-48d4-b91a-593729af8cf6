import fs from "fs";
import path from "path";
import dayjs from "dayjs";

/**
 * TradingMemoryHandler - Manages AI trading analysis memory and recommendations
 * Provides persistence, condition tracking, and historical context for trading decisions
 */
export class TradingMemoryHandler {
  constructor(options = {}) {
    this.memoryDir = options.memoryDir || "./trading-memory";
    this.maxHistoryDays = options.maxHistoryDays || 7; // Keep 7 days of history
    this.maxEntriesPerSymbol = options.maxEntriesPerSymbol || 50; // Max entries per symbol

    // Ensure memory directory exists
    this.ensureMemoryDirectory();

    // In-memory cache for fast access
    this.memoryCache = new Map();

    // Load existing memory on startup
    this.loadAllMemory();
  }

  /**
   * Ensure the memory directory exists
   */
  ensureMemoryDirectory() {
    if (!fs.existsSync(this.memoryDir)) {
      fs.mkdirSync(this.memoryDir, { recursive: true });
      console.log(`📁 Created trading memory directory: ${this.memoryDir}`);
    }
  }

  /**
   * Get memory file path for a symbol
   */
  getMemoryFilePath(symbol) {
    return path.join(this.memoryDir, `${symbol.toLowerCase()}-memory.json`);
  }

  /**
   * Load memory for a specific symbol
   */
  loadSymbolMemory(symbol) {
    const filePath = this.getMemoryFilePath(symbol);

    if (!fs.existsSync(filePath)) {
      return [];
    }

    try {
      const data = fs.readFileSync(filePath, "utf8");
      const memory = JSON.parse(data);

      // Clean old entries
      const cleanedMemory = this.cleanOldEntries(memory);

      // Cache in memory
      this.memoryCache.set(symbol.toLowerCase(), cleanedMemory);

      return cleanedMemory;
    } catch (error) {
      console.error(`❌ Error loading memory for ${symbol}:`, error.message);
      return [];
    }
  }

  /**
   * Load all symbol memories on startup
   */
  loadAllMemory() {
    try {
      const files = fs.readdirSync(this.memoryDir);
      const memoryFiles = files.filter((file) => file.endsWith("-memory.json"));

      for (const file of memoryFiles) {
        const symbol = file.replace("-memory.json", "").toUpperCase();
        this.loadSymbolMemory(symbol);
      }

      console.log(`📚 Loaded memory for ${memoryFiles.length} symbols`);
    } catch (error) {
      console.error("❌ Error loading trading memory:", error.message);
    }
  }

  /**
   * Save memory for a specific symbol
   */
  saveSymbolMemory(symbol, memory) {
    const filePath = this.getMemoryFilePath(symbol);

    try {
      // Clean and limit entries before saving
      const cleanedMemory = this.cleanOldEntries(memory);
      const limitedMemory = this.limitEntries(cleanedMemory);

      fs.writeFileSync(filePath, JSON.stringify(limitedMemory, null, 2));

      // Update cache
      this.memoryCache.set(symbol.toLowerCase(), limitedMemory);

      return true;
    } catch (error) {
      console.error(`❌ Error saving memory for ${symbol}:`, error.message);
      return false;
    }
  }

  /**
   * Clean old entries based on maxHistoryDays
   */
  cleanOldEntries(memory) {
    const cutoffDate = dayjs().subtract(this.maxHistoryDays, "day");

    return memory.filter((entry) => {
      const entryDate = dayjs(entry.timestamp);
      return entryDate.isAfter(cutoffDate);
    });
  }

  /**
   * Limit entries to maxEntriesPerSymbol (keep most recent)
   */
  limitEntries(memory) {
    if (memory.length <= this.maxEntriesPerSymbol) {
      return memory;
    }

    // Sort by timestamp (newest first) and take the most recent entries
    return memory
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, this.maxEntriesPerSymbol);
  }

  /**
   * Store AI trading analysis and recommendations
   */
  storeAnalysis(symbol, analysisData) {
    const memory = this.getSymbolMemory(symbol);

    const entry = {
      id: this.generateEntryId(),
      timestamp: new Date().toISOString(),
      symbol: symbol.toUpperCase(),
      type: "AI_ANALYSIS",
      ...analysisData,
      // Track if conditions have been met
      conditionsMet: {
        entryTriggered: false,
        stopLossHit: false,
        takeProfitHit: false,
        invalidated: false,
      },
    };

    memory.push(entry);
    this.saveSymbolMemory(symbol, memory);

    console.log(`💾 Stored analysis for ${symbol}: ${entry.id}`);
    return entry.id;
  }

  /**
   * Get memory for a symbol (from cache or load from disk)
   */
  getSymbolMemory(symbol) {
    const symbolKey = symbol.toLowerCase();

    if (this.memoryCache.has(symbolKey)) {
      return [...this.memoryCache.get(symbolKey)];
    }

    return this.loadSymbolMemory(symbol);
  }

  /**
   * Get the most recent analysis for a symbol
   */
  getLatestAnalysis(symbol) {
    const memory = this.getSymbolMemory(symbol);

    if (memory.length === 0) {
      return null;
    }

    // Return the most recent analysis
    return memory
      .filter((entry) => entry.type === "AI_ANALYSIS")
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))[0];
  }

  /**
   * Get recent analyses for context (last N entries)
   */
  getRecentAnalyses(symbol, count = 3) {
    const memory = this.getSymbolMemory(symbol);

    return memory
      .filter((entry) => entry.type === "AI_ANALYSIS")
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, count);
  }

  /**
   * Check if any previous conditions have been met
   */
  checkConditions(symbol, currentPrice) {
    const memory = this.getSymbolMemory(symbol);
    const updates = [];

    for (const entry of memory) {
      if (entry.type !== "AI_ANALYSIS" || !entry.trading_recommendation) {
        continue;
      }

      const conditions = entry.conditionsMet;
      const recommendation = entry.trading_recommendation;

      // Check entry conditions
      if (
        !conditions.entryTriggered &&
        this.checkEntryCondition(recommendation, currentPrice)
      ) {
        conditions.entryTriggered = true;
        conditions.entryPrice = currentPrice;
        conditions.entryTime = new Date().toISOString();
        updates.push({
          entryId: entry.id,
          type: "ENTRY_TRIGGERED",
          price: currentPrice,
          originalRecommendation: recommendation,
        });
      }

      // Check stop loss
      if (
        conditions.entryTriggered &&
        !conditions.stopLossHit &&
        this.checkStopLoss(recommendation, currentPrice)
      ) {
        conditions.stopLossHit = true;
        conditions.stopLossTime = new Date().toISOString();
        updates.push({
          entryId: entry.id,
          type: "STOP_LOSS_HIT",
          price: currentPrice,
          originalRecommendation: recommendation,
        });
      }

      // Check take profit
      if (
        conditions.entryTriggered &&
        !conditions.takeProfitHit &&
        this.checkTakeProfit(recommendation, currentPrice)
      ) {
        conditions.takeProfitHit = true;
        conditions.takeProfitTime = new Date().toISOString();
        updates.push({
          entryId: entry.id,
          type: "TAKE_PROFIT_HIT",
          price: currentPrice,
          originalRecommendation: recommendation,
        });
      }
    }

    // Save updates if any
    if (updates.length > 0) {
      this.saveSymbolMemory(symbol, memory);
    }

    return updates;
  }

  /**
   * Generate unique entry ID
   */
  generateEntryId() {
    return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * Check if entry condition is met
   */
  checkEntryCondition(recommendation, currentPrice) {
    if (!recommendation.entry_price || !recommendation.action) {
      return false;
    }

    const action = recommendation.action.toUpperCase();
    const entryPrice = parseFloat(recommendation.entry_price);

    if (action.includes("LONG") || action.includes("BUY")) {
      // For long positions, entry is triggered when price goes above entry level
      return currentPrice >= entryPrice;
    } else if (action.includes("SHORT") || action.includes("SELL")) {
      // For short positions, entry is triggered when price goes below entry level
      return currentPrice <= entryPrice;
    }

    return false;
  }

  /**
   * Check if stop loss is hit
   */
  checkStopLoss(recommendation, currentPrice) {
    if (!recommendation.stop_loss) {
      return false;
    }

    const stopLoss = parseFloat(recommendation.stop_loss);
    const action = recommendation.action.toUpperCase();

    if (action.includes("LONG") || action.includes("BUY")) {
      // For long positions, stop loss is hit when price goes below SL
      return currentPrice <= stopLoss;
    } else if (action.includes("SHORT") || action.includes("SELL")) {
      // For short positions, stop loss is hit when price goes above SL
      return currentPrice >= stopLoss;
    }

    return false;
  }

  /**
   * Check if take profit is hit
   */
  checkTakeProfit(recommendation, currentPrice) {
    if (!recommendation.take_profit_1 && !recommendation.take_profit) {
      return false;
    }

    const takeProfit = parseFloat(
      recommendation.take_profit_1 || recommendation.take_profit
    );
    const action = recommendation.action.toUpperCase();

    if (action.includes("LONG") || action.includes("BUY")) {
      // For long positions, TP is hit when price goes above TP level
      return currentPrice >= takeProfit;
    } else if (action.includes("SHORT") || action.includes("SELL")) {
      // For short positions, TP is hit when price goes below TP level
      return currentPrice <= takeProfit;
    }

    return false;
  }

  /**
   * Get historical context for AI analysis
   */
  getHistoricalContext(symbol, currentPrice) {
    const recentAnalyses = this.getRecentAnalyses(symbol, 3);
    const conditionUpdates = this.checkConditions(symbol, currentPrice);

    const context = {
      hasHistory: recentAnalyses.length > 0,
      recentAnalyses: recentAnalyses.map((analysis) => ({
        timestamp: analysis.timestamp,
        recommendation: analysis.trading_recommendation,
        majorTrend: analysis.major_trend,
        confidence: analysis.overall_assessment?.final_confidence,
        conditionsMet: analysis.conditionsMet,
      })),
      conditionUpdates,
      summary: this.generateContextSummary(
        recentAnalyses,
        conditionUpdates,
        currentPrice
      ),
    };

    return context;
  }

  /**
   * Generate a summary of historical context
   */
  generateContextSummary(recentAnalyses, conditionUpdates, currentPrice) {
    if (recentAnalyses.length === 0) {
      return "No previous analysis found for this symbol.";
    }

    const latest = recentAnalyses[0];
    const timeSinceLatest = dayjs().diff(dayjs(latest.timestamp), "minute");

    let summary = `Last analysis: ${timeSinceLatest}m ago. `;

    if (latest.trading_recommendation) {
      const rec = latest.trading_recommendation;
      summary += `Previous recommendation: ${rec.action} at ${rec.entry_price}. `;

      if (latest.conditionsMet.entryTriggered) {
        summary += "Entry was triggered. ";
      } else if (rec.entry_price) {
        const entryPrice = parseFloat(rec.entry_price);
        const distance = (
          ((currentPrice - entryPrice) / entryPrice) *
          100
        ).toFixed(2);
        summary += `Entry level ${
          distance > 0 ? "above" : "below"
        } current price by ${Math.abs(distance)}%. `;
      }
    }

    if (conditionUpdates.length > 0) {
      summary += `Recent updates: ${conditionUpdates
        .map((u) => u.type)
        .join(", ")}. `;
    }

    return summary.trim();
  }

  /**
   * Clear memory for a symbol
   */
  clearSymbolMemory(symbol) {
    const filePath = this.getMemoryFilePath(symbol);

    try {
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
      }

      this.memoryCache.delete(symbol.toLowerCase());
      console.log(`🗑️ Cleared memory for ${symbol}`);
      return true;
    } catch (error) {
      console.error(`❌ Error clearing memory for ${symbol}:`, error.message);
      return false;
    }
  }

  /**
   * Clear all memory
   */
  clearAllMemory() {
    try {
      const files = fs.readdirSync(this.memoryDir);
      const memoryFiles = files.filter((file) => file.endsWith("-memory.json"));

      for (const file of memoryFiles) {
        fs.unlinkSync(path.join(this.memoryDir, file));
      }

      this.memoryCache.clear();
      console.log(
        `🗑️ Cleared all trading memory (${memoryFiles.length} files)`
      );
      return true;
    } catch (error) {
      console.error("❌ Error clearing all memory:", error.message);
      return false;
    }
  }

  /**
   * Get memory statistics
   */
  getMemoryStats() {
    const stats = {
      totalSymbols: this.memoryCache.size,
      totalEntries: 0,
      oldestEntry: null,
      newestEntry: null,
      symbolStats: {},
    };

    for (const [symbol, memory] of this.memoryCache.entries()) {
      const symbolStat = {
        entries: memory.length,
        oldestEntry: null,
        newestEntry: null,
        activeRecommendations: 0,
      };

      if (memory.length > 0) {
        const sorted = memory.sort(
          (a, b) => new Date(a.timestamp) - new Date(b.timestamp)
        );
        symbolStat.oldestEntry = sorted[0].timestamp;
        symbolStat.newestEntry = sorted[sorted.length - 1].timestamp;

        // Count active recommendations (not yet invalidated)
        symbolStat.activeRecommendations = memory.filter(
          (entry) =>
            entry.type === "AI_ANALYSIS" &&
            !entry.conditionsMet.invalidated &&
            !entry.conditionsMet.stopLossHit &&
            !entry.conditionsMet.takeProfitHit
        ).length;

        // Update global stats
        stats.totalEntries += memory.length;

        if (!stats.oldestEntry || sorted[0].timestamp < stats.oldestEntry) {
          stats.oldestEntry = sorted[0].timestamp;
        }

        if (
          !stats.newestEntry ||
          sorted[sorted.length - 1].timestamp > stats.newestEntry
        ) {
          stats.newestEntry = sorted[sorted.length - 1].timestamp;
        }
      }

      stats.symbolStats[symbol.toUpperCase()] = symbolStat;
    }

    return stats;
  }
}

// Create default instance for global use
export const tradingMemoryHandler = new TradingMemoryHandler();

// Export the class as default
export default TradingMemoryHandler;

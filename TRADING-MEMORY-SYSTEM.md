# Trading Memory System

## Overview

The Trading Memory System solves the critical problem where your AI trading bot doesn't retain previous analysis and recommendations, leading to inconsistent trading decisions. Now your AI can:

- **Remember previous recommendations** and price levels
- **Track when conditions are met** (entry triggers, stop losses, take profits)
- **Provide consistent analysis** based on historical context
- **Build upon previous analysis** rather than starting fresh each time

## The Problem It Solves

**Before:** 
- ETH at $4240: AI recommends "wait for breakout above $4250"
- ETH at $4260: AI gives new recommendation "wait for $4300" (ignoring previous $4250 breakout)

**After:**
- ETH at $4240: AI recommends "wait for breakout above $4250" ✅ *Stored in memory*
- ETH at $4260: AI recognizes "Previous breakout condition at $4250 was met, entry triggered" ✅ *Consistent decision*

## Key Features

### 1. **Persistent Memory Storage**
- Stores AI analysis and recommendations in JSON files
- Survives bot restarts and system reboots
- Automatic cleanup of old entries (configurable retention period)

### 2. **Condition Tracking**
- Monitors entry price triggers
- Tracks stop loss hits
- Detects take profit achievements
- Identifies invalidation conditions

### 3. **Historical Context**
- Provides recent analysis history to AI
- Generates context summaries for decision making
- Tracks condition updates and status changes

### 4. **Memory Management Commands**
- `memorystats` - View memory statistics
- `clearmemory` - Clear all trading memory
- `btc_memory` or `eth_history` - View symbol-specific memory

## Architecture

### TradingMemoryHandler Class

```javascript
import { TradingMemoryHandler, tradingMemoryHandler } from './src/handlers/TradingMemoryHandler.js';

// Store analysis
const entryId = tradingMemoryHandler.storeAnalysis(symbol, aiTradingSignal);

// Get historical context
const context = tradingMemoryHandler.getHistoricalContext(symbol, currentPrice);

// Check condition updates
const updates = tradingMemoryHandler.checkConditions(symbol, currentPrice);
```

### Memory Entry Structure

```json
{
  "id": "1703123456789-abc123def",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "symbol": "ETHUSDT",
  "type": "AI_ANALYSIS",
  "major_trend": {
    "direction": "BULLISH",
    "strength": 7,
    "confidence": 8
  },
  "trading_recommendation": {
    "action": "BUY",
    "entry_price": "4250",
    "stop_loss": "4200",
    "take_profit_1": "4300"
  },
  "conditionsMet": {
    "entryTriggered": false,
    "stopLossHit": false,
    "takeProfitHit": false,
    "invalidated": false
  }
}
```

## Integration Points

### 1. **AI Analysis Enhancement**
The AI prompt now includes historical context:

```
LỊCH SỬ PHÂN TÍCH TRƯỚC ĐÓ:
- Tóm tắt: Last analysis: 15m ago. Previous recommendation: BUY at 4250. Entry level below current price by 0.24%.
- Phân tích gần nhất: BUY tại 4250
- Điều kiện đã kích hoạt: ENTRY_TRIGGERED
- Xu hướng chính trước: BULLISH
```

### 2. **Condition Monitoring**
Automatic condition checking in the main bot loop:

```javascript
// Check for condition updates
const conditionUpdates = tradingMemoryHandler.checkConditions(symbol, currentPrice);

if (conditionUpdates.length > 0) {
  console.log(`🎯 Condition updates: ${conditionUpdates.map(u => u.type).join(', ')}`);
}
```

### 3. **Enhanced Captions**
Telegram messages now include condition updates:

```
🎯 Updates: Entry triggered at 4260, Take profit reached
```

## Configuration

```javascript
const memoryHandler = new TradingMemoryHandler({
  memoryDir: './trading-memory',     // Storage directory
  maxHistoryDays: 7,                 // Keep 7 days of history
  maxEntriesPerSymbol: 50            // Max 50 entries per symbol
});
```

## File Structure

```
trading-memory/
├── ethusdt-memory.json
├── btcusdt-memory.json
└── adausdt-memory.json
```

## Usage Examples

### Basic Usage

```javascript
// The memory system is automatically integrated into runBot()
await runBot('ETHUSDT', chatId);
// ✅ Analysis stored, conditions checked, context provided
```

### Manual Memory Operations

```javascript
// Get recent analyses
const recent = tradingMemoryHandler.getRecentAnalyses('ETHUSDT', 3);

// Check specific conditions
const updates = tradingMemoryHandler.checkConditions('ETHUSDT', 4260);

// Get memory statistics
const stats = tradingMemoryHandler.getMemoryStats();

// Clear symbol memory
tradingMemoryHandler.clearSymbolMemory('ETHUSDT');
```

### Telegram Commands

```
/zizi memorystats          # View memory statistics
/zizi clearmemory         # Clear all memory
/zizi btc_memory          # View BTC trading history
/zizi eth_history         # View ETH trading history
```

## Testing

Run the test script to see the memory system in action:

```bash
node test-memory-system.js
```

This demonstrates:
- Storing AI analysis
- Tracking condition updates
- Historical context generation
- The ETH breakout scenario from your example

## Benefits

1. **Consistent Trading Decisions** - AI remembers previous recommendations
2. **Condition Awareness** - Tracks when price levels are hit
3. **Historical Context** - Builds upon previous analysis
4. **Debugging Support** - View trading history and memory stats
5. **Persistence** - Survives bot restarts
6. **Automatic Cleanup** - Manages memory size and retention

## Memory Management

- **Automatic cleanup** of entries older than `maxHistoryDays`
- **Size limiting** to `maxEntriesPerSymbol` per symbol
- **Efficient caching** for fast access
- **Error handling** with fallback mechanisms

Your AI trading bot now has a memory system that ensures consistent, context-aware trading decisions! 🧠📈
